import { createTRPCNext } from '@trpc/next';
import { httpBatchLink } from '@trpc/client';
import { ReagentWithStatus } from '@haocai/shared/types/reagent';
import type { AppRouter, BatchStockInResult } from '@/types/api';

/**
 * 获取基础URL
 */
function getBaseUrl() {
  if (typeof window !== 'undefined') return ''; // 浏览器环境使用相对路径
  if (process.env.VERCEL_URL) return `https://${process.env.VERCEL_URL}`; // Vercel环境
  return `http://localhost:${process.env.PORT ?? 3000}`; // 开发环境
}

// Mock 试剂数据用于开发
const mockReagent: ReagentWithStatus = {
  id: '1',
  code: 'YF03.001',
  name: '亘诺细胞冻存液',
  specification: '100ml',
  category: 'BIOLOGICAL_REAGENT',
  supplier: '亘诺生物',
  manufacturer: '亘诺生物科技有限公司',
  casNumber: '12345-67-8',
  formula: 'C6H12O6',
  molecularWeight: '180.16',
  purity: '99.5%',
  storageCondition: '-20°C',
  safetyLevel: '低',
  currentStock: '25',
  minThreshold: '5',
  maxCapacity: '100',
  unit: '瓶',
  unitPrice: '150.00',
  totalPrice: '3750.00',
  location: 'A-01-01',
  description: '用于细胞冷冻保存的专用冻存液',
  isActive: true,
  createdAt: '2024-01-15T10:30:00Z',
  updatedAt: '2024-01-20T14:45:00Z',
  createdBy: 'admin',
  updatedBy: 'admin',
  stockStatus: 'IN_STOCK' as any,
  lastTransaction: {
    createdAt: '2024-01-20T14:45:00Z',
    type: '入库'
  }
};

/**
 * tRPC 客户端配置
 */
export const trpc = createTRPCNext<any>({
  config() {
    return {
      links: [
        httpBatchLink({
          url: `${getBaseUrl()}/api/trpc`,
          headers() {
            return {
              // 添加认证头部（如果需要）
              // Authorization: `Bearer ${getAuthToken()}`,
            };
          },
        }),
      ],
      queryClientConfig: {
        defaultOptions: {
          queries: {
            staleTime: 60 * 1000, // 1分钟
            retry: 1,
          },
          mutations: {
            retry: 1,
          },
        },
      },
    };
  },
  ssr: false, // 禁用服务端渲染
}) as any; // 临时类型断言，直到后端类型正确导入

// 类型导出
export type RouterInputs = any; // TODO: 从后端导入真实类型
export type RouterOutputs = any; // TODO: 从后端导入真实类型

// 重新导出类型
export type { BatchStockInResult } from '@/types/api';

// Export mock data for testing purposes
export { mockReagent };
