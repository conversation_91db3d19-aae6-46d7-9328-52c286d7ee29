/**
 * API 类型定义
 * 临时类型定义，直到 tRPC 类型导入问题解决
 */

// 批量入库结果类型
export interface BatchStockInResult {
  id: string;
  reagentId: string;
  type: string;
  quantity: number;
  beforeStock: number;
  afterStock: number;
  success: boolean;
  message?: string;
}

// 批量入库输入类型
export interface BatchStockInInput {
  items: {
    reagentId: string;
    quantity: number;
    reason?: string;
    batchNumber?: string;
    expiryDate?: string;
    supplier?: string;
    price?: number;
    invoiceNumber?: string;
    notes?: string;
  }[];
}

// 模拟 AppRouter 类型
export interface AppRouter {
  stock: {
    batchStockIn: {
      useMutation: (options?: {
        onSuccess?: (data: BatchStockInResult[]) => void;
        onError?: (error: any) => void;
      }) => {
        mutateAsync: (input: BatchStockInInput) => Promise<BatchStockInResult[]>;
        isLoading: boolean;
      };
    };
  };
  reagent: {
    search: any;
    getById: any;
  };
}
