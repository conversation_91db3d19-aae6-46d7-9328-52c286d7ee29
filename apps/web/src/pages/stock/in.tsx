import React, { useState, useCallback } from 'react';
import { Layout, Typography, Card, Button, message, Menu, Steps } from 'antd';
import { PlusOutlined, ArrowLeftOutlined, HomeOutlined, SearchOutlined, InboxOutlined } from '@ant-design/icons';
import { useRouter } from 'next/router';
import { ReagentSelector, SelectedReagent } from '@/components/features/ReagentSelector';
import { BatchStockInManager, BatchStockInItem } from '@/components/features/BatchStockInManager';
import { StockInConfirmDialog } from '@/components/features/StockInConfirmDialog';
import { StockInSuccessResult, StockInResult } from '@/components/features/StockInSuccessResult';
import { ErrorBoundary } from '@/components/common/ErrorBoundary';
import { trpc } from '@/utils/trpc';
import type { BatchStockInResult } from '@/types/api';

const { Header, Content } = Layout;
const { Title } = Typography;

/**
 * 入库步骤枚举
 */
enum StockInStep {
  SELECT_REAGENTS = 0,
  CONFIGURE_DETAILS = 1,
  CONFIRM = 2,
  RESULT = 3
}

/**
 * 试剂入库页面
 * 支持单个和批量试剂入库操作
 */
export default function StockInPage() {
  const router = useRouter();

  // 入库流程状态
  const [currentStep, setCurrentStep] = useState<StockInStep>(StockInStep.SELECT_REAGENTS);
  const [selectedReagents, setSelectedReagents] = useState<SelectedReagent[]>([]);
  const [confirmDialogVisible, setConfirmDialogVisible] = useState(false);
  const [stockInResult, setStockInResult] = useState<StockInResult | null>(null);

  // 返回上一页
  const handleGoBack = () => {
    router.back();
  };

  // 导航到试剂搜索页面
  const handleGoToReagents = () => {
    router.push('/reagents');
  };

  // 导航到仪表板
  const handleGoToDashboard = () => {
    router.push('/dashboard');
  };

  // 处理试剂选择变化
  const handleReagentSelectionChange = useCallback((newSelection: SelectedReagent[]) => {
    setSelectedReagents(newSelection);
  }, []);

  // 处理进入下一步
  const handleNextStep = useCallback(() => {
    if (currentStep === StockInStep.SELECT_REAGENTS && selectedReagents.length === 0) {
      message.warning('请先选择要入库的试剂');
      return;
    }

    if (currentStep === StockInStep.CONFIGURE_DETAILS) {
      setConfirmDialogVisible(true);
      return;
    }

    setCurrentStep(currentStep + 1);
  }, [currentStep, selectedReagents.length]);

  // 处理返回上一步
  const handlePrevStep = useCallback(() => {
    if (currentStep > StockInStep.SELECT_REAGENTS) {
      setCurrentStep(currentStep - 1);
    }
  }, [currentStep]);

  // tRPC 批量入库 mutation
  const batchStockInMutation = trpc.stock.batchStockIn.useMutation({
    onSuccess: (results: BatchStockInResult[]) => {
      const successResults = results.filter((r: BatchStockInResult) => r.success);
      const failedResults = results.filter((r: BatchStockInResult) => !r.success);

      if (failedResults.length > 0) {
        message.error(`${failedResults.length} 个试剂入库失败`);
        return;
      }

      const mockResult: StockInResult = {
        success: true,
        message: '入库操作成功完成',
        transactionId: `TXN-${Date.now()}`,
        timestamp: new Date(),
        items: selectedReagents.map((selected, index) => ({
          id: `${selected.reagent.id}-${index}`,
          reagentId: selected.reagent.id,
          reagent: selected.reagent,
          quantity: selected.quantity || 1,
          batchNumber: selected.batchNumber,
          expiryDate: selected.expiryDate,
          reason: '批量入库',
          notes: selected.notes
        })),
        summary: {
          totalItems: successResults.length,
          totalQuantity: successResults.reduce((sum: number, r: BatchStockInResult) => sum + r.quantity, 0),
          totalValue: successResults.reduce((sum: number, r: BatchStockInResult) => {
            const unitPrice = Number(selectedReagents.find(s => s.reagent.id === r.reagentId)?.reagent.unitPrice) || 0;
            return sum + (unitPrice * r.quantity);
          }, 0),
          affectedCategories: [...new Set(selectedReagents.map(s => s.reagent.category))]
        }
      };

      setStockInResult(mockResult);
      setCurrentStep(StockInStep.RESULT);
      setConfirmDialogVisible(false);
      message.success('入库操作成功完成！');
    },
    onError: (error: any) => {
      message.error(`入库操作失败: ${error.message}`);
      console.error('入库失败:', error);
    }
  });

  // 处理批量提交入库
  const handleBatchSubmit = async (items: BatchStockInItem[]) => {
    try {
      // 转换为 API 格式
      const apiItems = items.map(item => ({
        reagentId: item.reagentId,
        quantity: item.quantity,
        reason: item.reason,
        batchNumber: item.batchNumber,
        expiryDate: item.expiryDate?.toISOString(),
        notes: item.notes
      }));

      await batchStockInMutation.mutateAsync({ items: apiItems });
    } catch (error) {
      // 错误已在 onError 中处理
    }
  };

  // 处理重新开始入库
  const handleRestartStockIn = () => {
    setCurrentStep(StockInStep.SELECT_REAGENTS);
    setSelectedReagents([]);
    setStockInResult(null);
    setConfirmDialogVisible(false);
  };

  // 导航菜单项
  const navigationItems = [
    {
      key: 'dashboard',
      icon: <HomeOutlined />,
      label: '仪表板',
      onClick: handleGoToDashboard,
    },
    {
      key: 'reagents',
      icon: <SearchOutlined />,
      label: '试剂搜索',
      onClick: handleGoToReagents,
    },
    {
      key: 'stock',
      icon: <InboxOutlined />,
      label: '库存管理',
      children: [
        {
          key: 'stock-in',
          label: '试剂入库',
          onClick: () => router.push('/stock/in'),
        },
      ],
    },
  ];

  return (
    <ErrorBoundary>
      <Layout className="min-h-screen bg-gray-50">
        <Header className="bg-white shadow-sm border-b border-gray-200">
          <div className="flex items-center justify-between h-full max-w-7xl mx-auto px-4">
            <div className="flex items-center space-x-6">
              <div className="flex items-center space-x-4">
                <Button
                  type="text"
                  icon={<ArrowLeftOutlined />}
                  onClick={handleGoBack}
                  className="flex items-center"
                >
                  返回
                </Button>
                <Title level={3} className="!mb-0 !text-gray-800">
                  试剂入库
                </Title>
              </div>

              {/* 主导航菜单 */}
              <Menu
                mode="horizontal"
                selectedKeys={['stock-in']}
                className="border-none bg-transparent flex-1"
                items={navigationItems}
              />
            </div>
          </div>
        </Header>

        <Content className="p-4 lg:p-6">
          <div className="max-w-7xl mx-auto">
            {/* 步骤指示器 */}
            <Card className="mb-4 lg:mb-6">
              <Steps
                current={currentStep}
                className="mb-4"
                direction="horizontal"
                size="small"
                responsive={true}
                items={[
                  {
                    title: <span className="hidden sm:inline">选择试剂</span>,
                    description: <span className="hidden md:inline">搜索并选择要入库的试剂</span>
                  },
                  {
                    title: <span className="hidden sm:inline">配置详情</span>,
                    description: <span className="hidden md:inline">填写入库数量、批次等信息</span>
                  },
                  {
                    title: <span className="hidden sm:inline">确认入库</span>,
                    description: <span className="hidden md:inline">核对信息并确认提交</span>
                  },
                  {
                    title: <span className="hidden sm:inline">完成</span>,
                    description: <span className="hidden md:inline">查看入库结果</span>
                  }
                ]}
              />

              {/* 移动端步骤说明 */}
              <div className="block sm:hidden mt-4 p-3 bg-gray-50 rounded">
                <div className="text-sm font-medium text-gray-700">
                  {currentStep === StockInStep.SELECT_REAGENTS && '第1步：选择入库试剂'}
                  {currentStep === StockInStep.CONFIGURE_DETAILS && '第2步：配置入库详情'}
                  {currentStep === StockInStep.CONFIRM && '第3步：确认入库信息'}
                  {currentStep === StockInStep.RESULT && '第4步：查看入库结果'}
                </div>
              </div>
            </Card>

            {/* 步骤内容 */}
            {currentStep === StockInStep.SELECT_REAGENTS && (
              <Card>
                <div className="mb-4">
                  <Title level={4} className="!mb-2">
                    选择入库试剂
                  </Title>
                  <p className="text-gray-600">
                    搜索并选择需要入库的试剂，支持多选和批量操作
                  </p>
                </div>

                <ReagentSelector
                  selectedReagents={selectedReagents}
                  onSelectionChange={handleReagentSelectionChange}
                  multiSelect={true}
                  placeholder="搜索试剂名称、编码或供应商..."
                />

                {selectedReagents.length > 0 && (
                  <div className="flex flex-col sm:flex-row justify-end mt-6 pt-4 border-t border-gray-200 gap-3">
                    <Button
                      type="primary"
                      onClick={handleNextStep}
                      size="large"
                      className="w-full sm:w-auto"
                    >
                      下一步：配置详情 ({selectedReagents.length}个)
                    </Button>
                  </div>
                )}
              </Card>
            )}

            {currentStep === StockInStep.CONFIGURE_DETAILS && (
              <div className="space-y-6">
                <BatchStockInManager
                  selectedReagents={selectedReagents}
                  onItemUpdate={(itemId, data) => {
                    // 更新选中试剂的详情
                    const updatedReagents = selectedReagents.map(item => {
                      if (`${item.reagent.id}-${selectedReagents.indexOf(item)}` === itemId) {
                        return {
                          ...item,
                          quantity: data.quantity,
                          batchNumber: data.batchNumber,
                          expiryDate: data.expiryDate,
                          notes: data.notes
                        };
                      }
                      return item;
                    });
                    setSelectedReagents(updatedReagents);
                  }}
                  onItemRemove={(itemId) => {
                    const index = parseInt(itemId.split('-')[1]);
                    const updatedReagents = selectedReagents.filter((_, i) => i !== index);
                    setSelectedReagents(updatedReagents);
                  }}
                  onBatchSubmit={handleNextStep}
                  loading={batchStockInMutation.isLoading}
                />

                <div className="flex flex-col sm:flex-row justify-between gap-3">
                  <Button
                    onClick={handlePrevStep}
                    size="large"
                    className="w-full sm:w-auto order-2 sm:order-1"
                  >
                    上一步
                  </Button>
                  <Button
                    type="primary"
                    onClick={handleNextStep}
                    size="large"
                    disabled={selectedReagents.length === 0}
                    className="w-full sm:w-auto order-1 sm:order-2"
                  >
                    下一步：确认入库
                  </Button>
                </div>
              </div>
            )}

            {currentStep === StockInStep.RESULT && stockInResult && (
              <StockInSuccessResult
                result={stockInResult}
                onContinueStockIn={handleRestartStockIn}
                onGoToDashboard={handleGoToDashboard}
                onPrintReport={() => message.info('打印功能开发中...')}
                onDownloadReport={() => message.info('下载功能开发中...')}
              />
            )}
          </div>
        </Content>

        {/* 入库确认对话框 */}
        <StockInConfirmDialog
          visible={confirmDialogVisible}
          items={selectedReagents.map((selected, index) => ({
            id: `${selected.reagent.id}-${index}`,
            reagentId: selected.reagent.id,
            reagent: selected.reagent,
            quantity: selected.quantity || 1,
            batchNumber: selected.batchNumber,
            expiryDate: selected.expiryDate,
            reason: '批量入库',
            notes: selected.notes
          }))}
          onConfirm={() => {
            const items = selectedReagents.map((selected, index) => ({
              id: `${selected.reagent.id}-${index}`,
              reagentId: selected.reagent.id,
              reagent: selected.reagent,
              quantity: selected.quantity || 1,
              batchNumber: selected.batchNumber,
              expiryDate: selected.expiryDate,
              reason: '批量入库',
              notes: selected.notes
            }));
            handleBatchSubmit(items);
          }}
          onCancel={() => setConfirmDialogVisible(false)}
          loading={batchStockInMutation.isLoading}
        />
      </Layout>
    </ErrorBoundary>
  );
}
