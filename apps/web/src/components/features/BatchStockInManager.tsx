import React, { useState, useCallback, useMemo } from 'react';
import {
  Card,
  Typography,
  Button,
  Table,
  Space,
  Tag,
  Popconfirm,
  Alert,
  Divider,
  Row,
  Col,
  Statistic,
  Modal,
  message
} from 'antd';
import {
  PlusOutlined,
  DeleteOutlined,
  EditOutlined,
  SaveOutlined,
  ClearOutlined,
  InfoCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import { ReagentWithStatus, CATEGORY_LABELS } from '@haocai/shared/types/reagent';
import { StockInForm, StockInFormData } from './StockInForm';
import { SelectedReagent } from './ReagentSelector';

const { Title, Text } = Typography;

/**
 * 批量入库项目接口
 */
export interface BatchStockInItem extends StockInFormData {
  id: string;
  reagent: ReagentWithStatus;
  isEditing?: boolean;
}

/**
 * 批量入库管理器组件属性
 */
interface BatchStockInManagerProps {
  selectedReagents: SelectedReagent[];
  onItemUpdate?: (itemId: string, data: StockInFormData) => void;
  onItemRemove?: (itemId: string) => void;
  onBatchSubmit?: (items: BatchStockInItem[]) => void;
  onClear?: () => void;
  loading?: boolean;
  className?: string;
}

/**
 * 批量入库管理器组件
 * 管理多个试剂的入库信息，支持编辑、删除和批量提交
 */
export const BatchStockInManager: React.FC<BatchStockInManagerProps> = React.memo(({
  selectedReagents,
  onItemUpdate,
  onItemRemove,
  onBatchSubmit,
  onClear,
  loading = false,
  className = ''
}) => {
  const [editingItemId, setEditingItemId] = useState<string | null>(null);
  const [editFormVisible, setEditFormVisible] = useState(false);
  const [editingItem, setEditingItem] = useState<BatchStockInItem | null>(null);

  // 将选中的试剂转换为批量入库项目 - 使用 useMemo 优化性能
  const batchItems: BatchStockInItem[] = useMemo(() =>
    selectedReagents.map((selected, index) => ({
      id: `${selected.reagent.id}-${index}`,
      reagentId: selected.reagent.id,
      reagent: selected.reagent,
      quantity: selected.quantity || 1,
      batchNumber: selected.batchNumber,
      expiryDate: selected.expiryDate,
      reason: '批量入库',
      notes: selected.notes
    })), [selectedReagents]);

  // 计算统计信息 - 使用 useMemo 优化性能
  const statistics = useMemo(() => ({
    totalItems: batchItems.length,
    totalQuantity: batchItems.reduce((sum, item) => sum + item.quantity, 0),
    categories: [...new Set(batchItems.map(item => item.reagent.category))].length,
    hasIncompleteItems: batchItems.some(item => !item.quantity || !item.reason)
  }), [batchItems]);

  // 处理编辑项目 - 使用 useCallback 优化性能
  const handleEditItem = useCallback((item: BatchStockInItem) => {
    setEditingItem(item);
    setEditingItemId(item.id);
    setEditFormVisible(true);
  }, []);

  // 处理保存编辑 - 使用 useCallback 优化性能
  const handleSaveEdit = useCallback((data: StockInFormData) => {
    if (editingItemId && onItemUpdate) {
      onItemUpdate(editingItemId, data);
      setEditFormVisible(false);
      setEditingItem(null);
      setEditingItemId(null);
      message.success('入库信息已更新');
    }
  }, [editingItemId, onItemUpdate]);

  // 处理取消编辑 - 使用 useCallback 优化性能
  const handleCancelEdit = useCallback(() => {
    setEditFormVisible(false);
    setEditingItem(null);
    setEditingItemId(null);
  }, []);

  // 处理删除项目
  const handleRemoveItem = (itemId: string) => {
    if (onItemRemove) {
      onItemRemove(itemId);
      message.success('已移除试剂');
    }
  };

  // 处理批量提交
  const handleBatchSubmit = () => {
    if (statistics.hasIncompleteItems) {
      message.error('请完善所有试剂的入库信息');
      return;
    }

    if (onBatchSubmit) {
      onBatchSubmit(batchItems);
    }
  };

  // 处理清空列表
  const handleClear = () => {
    if (onClear) {
      onClear();
    }
  };

  // 表格列定义
  const columns = [
    {
      title: '试剂信息',
      key: 'reagent',
      render: (record: BatchStockInItem) => (
        <div>
          <div className="font-medium">{record.reagent.name}</div>
          <div className="text-sm text-gray-500">
            编码: {record.reagent.code}
          </div>
          <div className="text-sm text-gray-500">
            规格: {record.reagent.specification || '未指定'}
          </div>
        </div>
      ),
      width: 200
    },
    {
      title: '分类',
      key: 'category',
      render: (record: BatchStockInItem) => (
        <Tag color="blue">
          {CATEGORY_LABELS[record.reagent.category]}
        </Tag>
      ),
      width: 120
    },
    {
      title: '当前库存',
      key: 'currentStock',
      render: (record: BatchStockInItem) => (
        <Text>
          {record.reagent.currentStock} {record.reagent.unit}
        </Text>
      ),
      width: 100
    },
    {
      title: '入库数量',
      key: 'quantity',
      render: (record: BatchStockInItem) => (
        <Text strong className={!record.quantity ? 'text-red-500' : 'text-green-600'}>
          {record.quantity || '未设置'} {record.reagent.unit}
        </Text>
      ),
      width: 100
    },
    {
      title: '批次号',
      key: 'batchNumber',
      render: (record: BatchStockInItem) => (
        <Text>{record.batchNumber || '-'}</Text>
      ),
      width: 120
    },
    {
      title: '过期日期',
      key: 'expiryDate',
      render: (record: BatchStockInItem) => (
        <Text>
          {record.expiryDate ? new Date(record.expiryDate).toLocaleDateString() : '-'}
        </Text>
      ),
      width: 120
    },
    {
      title: '入库原因',
      key: 'reason',
      render: (record: BatchStockInItem) => (
        <Text className={!record.reason ? 'text-red-500' : ''}>
          {record.reason || '未设置'}
        </Text>
      ),
      width: 150
    },
    {
      title: '操作',
      key: 'actions',
      render: (record: BatchStockInItem) => (
        <Space>
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => handleEditItem(record)}
            size="small"
          >
            编辑
          </Button>
          <Popconfirm
            title="确认删除"
            description="确定要移除这个试剂吗？"
            onConfirm={() => handleRemoveItem(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="text"
              icon={<DeleteOutlined />}
              danger
              size="small"
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
      width: 120,
      fixed: 'right' as const
    }
  ];

  if (batchItems.length === 0) {
    return (
      <Card className={className}>
        <div className="text-center py-12">
          <PlusOutlined className="text-4xl text-gray-300 mb-4" />
          <Title level={4} className="text-gray-400">
            暂无选中的试剂
          </Title>
          <Text className="text-gray-500">
            请先选择要入库的试剂
          </Text>
        </div>
      </Card>
    );
  }

  return (
    <div className={`batch-stock-in-manager ${className}`}>
      <Card>
        {/* 统计信息 */}
        <div className="mb-6">
          <Title level={4} className="!mb-4">
            批量入库管理
          </Title>
          <Row gutter={[16, 16]}>
            <Col xs={12} sm={6}>
              <Statistic
                title="试剂数量"
                value={statistics.totalItems}
                suffix="个"
                valueStyle={{ color: '#1890ff' }}
              />
            </Col>
            <Col xs={12} sm={6}>
              <Statistic
                title="总入库量"
                value={statistics.totalQuantity}
                precision={2}
                valueStyle={{ color: '#52c41a' }}
              />
            </Col>
            <Col xs={12} sm={6}>
              <Statistic
                title="涉及分类"
                value={statistics.categories}
                suffix="个"
                valueStyle={{ color: '#722ed1' }}
              />
            </Col>
            <Col xs={12} sm={6}>
              <Statistic
                title="完成度"
                value={((statistics.totalItems - (statistics.hasIncompleteItems ? 1 : 0)) / statistics.totalItems * 100)}
                precision={0}
                suffix="%"
                valueStyle={{
                  color: statistics.hasIncompleteItems ? '#fa8c16' : '#52c41a'
                }}
              />
            </Col>
          </Row>
        </div>

        {/* 警告提示 */}
        {statistics.hasIncompleteItems && (
          <Alert
            message="存在未完善的入库信息"
            description="请完善所有试剂的数量和入库原因信息后再提交"
            type="warning"
            showIcon
            className="mb-4"
          />
        )}

        <Divider />

        {/* 批量入库列表 */}
        <Table
          columns={columns}
          dataSource={batchItems}
          rowKey="id"
          pagination={false}
          scroll={{ x: 1000 }}
          size="middle"
          className="mb-6"
        />

        {/* 操作按钮 */}
        <div className="flex justify-between items-center pt-4 border-t border-gray-200">
          <div>
            <Button
              icon={<ClearOutlined />}
              onClick={handleClear}
              disabled={loading}
            >
              清空列表
            </Button>
          </div>
          <div className="flex gap-3">
            <Button
              type="primary"
              icon={<SaveOutlined />}
              onClick={handleBatchSubmit}
              loading={loading}
              disabled={statistics.hasIncompleteItems}
            >
              批量提交入库 ({statistics.totalItems}个)
            </Button>
          </div>
        </div>
      </Card>

      {/* 编辑表单模态框 */}
      <Modal
        title={`编辑入库信息 - ${editingItem?.reagent.name}`}
        open={editFormVisible}
        onCancel={handleCancelEdit}
        footer={null}
        width={800}
        destroyOnClose
      >
        {editingItem && (
          <StockInForm
            reagent={editingItem.reagent}
            initialData={{
              quantity: editingItem.quantity,
              batchNumber: editingItem.batchNumber,
              expiryDate: editingItem.expiryDate,
              reason: editingItem.reason,
              notes: editingItem.notes
            }}
            onSubmit={handleSaveEdit}
            onCancel={handleCancelEdit}
            loading={loading}
          />
        )}
      </Modal>
    </div>
  );
});
