import React, { useState, useMemo, useRef } from 'react';
import {
  Layout,
  Row,
  Col,
  Pagination,
  Spin,
  Empty,
  Alert,
  Typography,
  Space,
  Button,
  Drawer,
  Tooltip,
  Card,
  Tag,
  Checkbox,
  Badge,
  Modal
} from 'antd';
import {
  FilterOutlined,
  AppstoreOutlined,
  BarsOutlined,
  CheckOutlined,
  PlusOutlined,
  SearchOutlined,
  EyeOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import { SearchInput } from '@/components/ui/SearchInput';
import { FilterPanel } from './FilterPanel';
import { SortControls } from './SortControls';
import { useReagentSearch } from '@/hooks/useReagentSearch';
import { ReagentWithStatus, CATEGORY_LABELS, STOCK_STATUS_COLORS } from '@haocai/shared/types/reagent';

const { Content, Sider } = Layout;
const { Title, Text } = Typography;

/**
 * 选中的试剂信息
 */
export interface SelectedReagent {
  reagent: ReagentWithStatus;
  quantity?: number;
  batchNumber?: string;
  expiryDate?: Date;
  notes?: string;
}

/**
 * 试剂选择器组件属性
 */
interface ReagentSelectorProps {
  selectedReagents?: SelectedReagent[];
  onReagentSelect?: (reagent: ReagentWithStatus) => void;
  onReagentDeselect?: (reagentId: string) => void;
  onSelectionChange?: (selectedReagents: SelectedReagent[]) => void;
  multiSelect?: boolean;
  className?: string;
  placeholder?: string;
}

/**
 * 试剂选择器组件
 * 专门用于入库时选择试剂，支持搜索、筛选和选择功能
 */
export const ReagentSelector: React.FC<ReagentSelectorProps> = React.memo(({
  selectedReagents = [],
  onReagentSelect,
  onReagentDeselect,
  onSelectionChange,
  multiSelect = true,
  className = '',
  placeholder = '搜索试剂名称、编码或供应商...'
}) => {
  // 移动端筛选器抽屉状态
  const [filterDrawerVisible, setFilterDrawerVisible] = useState(false);

  // 视图模式状态
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  // 试剂详情模态框状态
  const [selectedReagentForDetail, setSelectedReagentForDetail] = useState<ReagentWithStatus | null>(null);
  const [detailModalVisible, setDetailModalVisible] = useState(false);

  // 搜索输入框引用已移除，因为 SearchInput 组件不支持 ref

  // 使用搜索 Hook
  const {
    searchState,
    reagents,
    total,
    loading,
    error,
    categoryStats,
    setQuery,
    setCategory,
    setSupplier,
    setLowStock,
    setPage,
    setPageSize,
    setSorting,
    resetFilters,
    totalPages
  } = useReagentSearch();

  // 获取供应商列表
  const suppliers = useMemo(() => {
    const supplierSet = new Set<string>();
    reagents.forEach(reagent => {
      if (reagent.supplier) {
        supplierSet.add(reagent.supplier);
      }
    });
    return Array.from(supplierSet).sort();
  }, [reagents]);

  // 检查试剂是否已选中
  const isReagentSelected = (reagentId: string) => {
    return selectedReagents.some(item => item.reagent.id === reagentId);
  };

  // 处理试剂选择
  const handleReagentSelect = (reagent: ReagentWithStatus) => {
    if (isReagentSelected(reagent.id)) {
      // 取消选择
      if (onReagentDeselect) {
        onReagentDeselect(reagent.id);
      }
      if (onSelectionChange) {
        const newSelection = selectedReagents.filter(item => item.reagent.id !== reagent.id);
        onSelectionChange(newSelection);
      }
    } else {
      // 选择试剂
      if (onReagentSelect) {
        onReagentSelect(reagent);
      }
      if (onSelectionChange) {
        const newSelection = multiSelect
          ? [...selectedReagents, { reagent }]
          : [{ reagent }];
        onSelectionChange(newSelection);
      }
    }
  };

  // 处理查看试剂详情
  const handleReagentView = (reagent: ReagentWithStatus, event: React.MouseEvent) => {
    event.stopPropagation(); // 阻止卡片点击事件
    setSelectedReagentForDetail(reagent);
    setDetailModalVisible(true);
  };

  // 关闭详情模态框
  const handleDetailModalClose = () => {
    setDetailModalVisible(false);
    setSelectedReagentForDetail(null);
  };

  // 渲染试剂选择卡片
  const renderReagentCard = (reagent: ReagentWithStatus) => {
    const isSelected = isReagentSelected(reagent.id);

    return (
      <Card
        key={reagent.id}
        className={`reagent-selector-card cursor-pointer transition-all duration-200 ${isSelected ? 'border-blue-500 bg-blue-50' : 'hover:border-gray-400'
          }`}
        size="small"
        hoverable
        onClick={() => handleReagentSelect(reagent)}
        extra={
          <div className="flex items-center gap-2">
            <Tooltip title="查看详情">
              <Button
                type="text"
                size="small"
                icon={<EyeOutlined />}
                onClick={(e) => handleReagentView(reagent, e)}
              />
            </Tooltip>
            <Checkbox
              checked={isSelected}
              onChange={() => handleReagentSelect(reagent)}
            />
          </div>
        }
      >
        {/* 试剂基本信息 */}
        <div className="space-y-2">
          <div className="flex justify-between items-start">
            <div className="flex-1 min-w-0">
              <Title level={5} className="!mb-1 truncate" title={reagent.name}>
                {reagent.name}
              </Title>
              <Text type="secondary" className="text-sm">
                编码: {reagent.code}
              </Text>
            </div>
            {isSelected && (
              <Badge
                count={<CheckOutlined className="text-blue-500" />}
                className="ml-2"
              />
            )}
          </div>

          {/* 规格和分类 */}
          <div className="flex justify-between items-center">
            <Text className="text-sm">
              {reagent.specification || '未指定规格'}
            </Text>
            <Tag color="blue" className="text-xs">
              {CATEGORY_LABELS[reagent.category]}
            </Tag>
          </div>

          {/* 供应商 */}
          {reagent.supplier && (
            <Text className="text-sm text-gray-600">
              供应商: {reagent.supplier}
            </Text>
          )}

          {/* 库存信息 */}
          <div className="flex justify-between items-center pt-2 border-t border-gray-100">
            <Text strong className="text-sm">
              库存: {reagent.currentStock} {reagent.unit}
            </Text>
            <Tag color={STOCK_STATUS_COLORS[reagent.stockStatus]} className="text-xs">
              {reagent.stockStatus}
            </Tag>
          </div>
        </div>
      </Card>
    );
  };

  // 渲染搜索结果
  const renderSearchResults = () => {
    if (loading) {
      return (
        <div className="flex justify-center items-center py-12">
          <Spin size="large" tip="搜索中..." />
        </div>
      );
    }

    if (error) {
      return (
        <Alert
          message="搜索失败"
          description={error}
          type="error"
          showIcon
          className="mb-4"
        />
      );
    }

    if (reagents.length === 0) {
      return (
        <Empty
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description={
            <span>
              {searchState.query ? '未找到匹配的试剂' : '请输入关键词搜索试剂'}
            </span>
          }
        />
      );
    }

    return (
      <>
        {/* 搜索结果网格 */}
        <Row gutter={[16, 16]} className="mb-6">
          {reagents.map(reagent => (
            <Col xs={24} sm={12} lg={8} xl={6} key={reagent.id}>
              {renderReagentCard(reagent)}
            </Col>
          ))}
        </Row>

        {/* 分页 */}
        {totalPages > 1 && (
          <div className="flex justify-center">
            <Pagination
              current={searchState.currentPage}
              total={total}
              pageSize={searchState.pageSize}
              onChange={setPage}
              onShowSizeChange={(current, size) => setPageSize(size)}
              showSizeChanger
              showQuickJumper
              showTotal={(total, range) =>
                `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
              }
            />
          </div>
        )}
      </>
    );
  };

  return (
    <div className={`reagent-selector ${className}`}>
      <Layout className="bg-white">
        {/* 桌面端侧边栏筛选器 */}
        <Sider
          width={280}
          className="hidden lg:block bg-gray-50 border-r"
          theme="light"
        >
          <div className="p-4">
            <FilterPanel
              category={searchState.category}
              supplier={searchState.supplier}
              lowStock={searchState.lowStock}
              categoryStats={categoryStats}
              suppliers={suppliers}
              onCategoryChange={setCategory}
              onSupplierChange={setSupplier}
              onLowStockChange={setLowStock}
              onReset={resetFilters}
            />
          </div>
        </Sider>

        {/* 主内容区域 */}
        <Content className="bg-white">
          {/* 搜索栏 */}
          <div className="p-4 border-b border-gray-200">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
              {/* 搜索输入框 */}
              <div className="flex-1 max-w-md">
                <SearchInput
                  placeholder={placeholder}
                  value={searchState.query}
                  onChange={setQuery}
                  allowClear
                  size="large"
                />
              </div>

              {/* 工具栏 */}
              <div className="flex items-center gap-2">
                {/* 移动端筛选按钮 */}
                <Button
                  icon={<FilterOutlined />}
                  onClick={() => setFilterDrawerVisible(true)}
                  className="lg:hidden"
                >
                  筛选
                </Button>

                {/* 排序控件 */}
                <SortControls
                  sortBy={searchState.sortBy}
                  sortOrder={searchState.sortOrder}
                  onSortChange={setSorting}
                />

                {/* 视图切换 */}
                <Button.Group>
                  <Button
                    icon={<AppstoreOutlined />}
                    type={viewMode === 'grid' ? 'primary' : 'default'}
                    onClick={() => setViewMode('grid')}
                  />
                  <Button
                    icon={<BarsOutlined />}
                    type={viewMode === 'list' ? 'primary' : 'default'}
                    onClick={() => setViewMode('list')}
                  />
                </Button.Group>
              </div>
            </div>

            {/* 选择状态显示 */}
            {selectedReagents.length > 0 && (
              <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded">
                <Text className="text-blue-700">
                  已选择 {selectedReagents.length} 个试剂
                </Text>
              </div>
            )}
          </div>

          {/* 搜索结果 */}
          <div className="p-4">
            {renderSearchResults()}
          </div>
        </Content>
      </Layout>

      {/* 移动端筛选器抽屉 */}
      <Drawer
        title="筛选条件"
        placement="right"
        onClose={() => setFilterDrawerVisible(false)}
        open={filterDrawerVisible}
        width={320}
        className="lg:hidden"
      >
        <FilterPanel
          category={searchState.category}
          supplier={searchState.supplier}
          lowStock={searchState.lowStock}
          categoryStats={categoryStats}
          suppliers={suppliers}
          onCategoryChange={setCategory}
          onSupplierChange={setSupplier}
          onLowStockChange={setLowStock}
          onReset={resetFilters}
        />
      </Drawer>

      {/* 试剂详情模态框 */}
      <Modal
        title={selectedReagentForDetail ? `试剂详情 - ${selectedReagentForDetail.name}` : '试剂详情'}
        open={detailModalVisible}
        onCancel={handleDetailModalClose}
        footer={[
          <Button key="close" onClick={handleDetailModalClose}>
            关闭
          </Button>,
          <Button
            key="select"
            type="primary"
            onClick={() => {
              if (selectedReagentForDetail) {
                handleReagentSelect(selectedReagentForDetail);
                handleDetailModalClose();
              }
            }}
          >
            {selectedReagentForDetail && isReagentSelected(selectedReagentForDetail.id) ? '取消选择' : '选择此试剂'}
          </Button>
        ]}
        width={800}
        className="reagent-detail-modal"
      >
        {selectedReagentForDetail && (
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Title level={5} className="!mb-2">基本信息</Title>
                <div className="space-y-2 text-sm">
                  <div><span className="text-gray-600">编码:</span> {selectedReagentForDetail.code}</div>
                  <div><span className="text-gray-600">名称:</span> {selectedReagentForDetail.name}</div>
                  <div><span className="text-gray-600">规格:</span> {selectedReagentForDetail.specification || '未指定'}</div>
                  <div><span className="text-gray-600">供应商:</span> {selectedReagentForDetail.supplier || '未指定'}</div>
                  <div><span className="text-gray-600">分类:</span> {CATEGORY_LABELS[selectedReagentForDetail.category]}</div>
                </div>
              </div>

              <div>
                <Title level={5} className="!mb-2">库存信息</Title>
                <div className="space-y-2 text-sm">
                  <div><span className="text-gray-600">当前库存:</span> {selectedReagentForDetail.currentStock} {selectedReagentForDetail.unit}</div>
                  <div><span className="text-gray-600">警戒线:</span> {selectedReagentForDetail.minThreshold} {selectedReagentForDetail.unit}</div>
                  <div><span className="text-gray-600">最大容量:</span> {selectedReagentForDetail.maxCapacity || '未设置'} {selectedReagentForDetail.unit}</div>
                  <div><span className="text-gray-600">存储位置:</span> {selectedReagentForDetail.location || '未指定'}</div>
                  <div><span className="text-gray-600">存储条件:</span> {selectedReagentForDetail.storageCondition || '未指定'}</div>
                </div>
              </div>
            </div>

            {selectedReagentForDetail.description && (
              <div>
                <Title level={5} className="!mb-2">描述</Title>
                <p className="text-sm text-gray-600">{selectedReagentForDetail.description}</p>
              </div>
            )}

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Title level={5} className="!mb-2">技术参数</Title>
                <div className="space-y-2 text-sm">
                  {selectedReagentForDetail.casNumber && (
                    <div><span className="text-gray-600">CAS号:</span> {selectedReagentForDetail.casNumber}</div>
                  )}
                  {selectedReagentForDetail.formula && (
                    <div><span className="text-gray-600">分子式:</span> {selectedReagentForDetail.formula}</div>
                  )}
                  {selectedReagentForDetail.molecularWeight && (
                    <div><span className="text-gray-600">分子量:</span> {selectedReagentForDetail.molecularWeight}</div>
                  )}
                  {selectedReagentForDetail.purity && (
                    <div><span className="text-gray-600">纯度:</span> {selectedReagentForDetail.purity}</div>
                  )}
                  {selectedReagentForDetail.safetyLevel && (
                    <div><span className="text-gray-600">安全等级:</span> {selectedReagentForDetail.safetyLevel}</div>
                  )}
                </div>
              </div>

              <div>
                <Title level={5} className="!mb-2">价格信息</Title>
                <div className="space-y-2 text-sm">
                  {selectedReagentForDetail.unitPrice && (
                    <div><span className="text-gray-600">单价:</span> ¥{Number(selectedReagentForDetail.unitPrice).toFixed(2)}</div>
                  )}
                  {selectedReagentForDetail.totalPrice && (
                    <div><span className="text-gray-600">总价:</span> ¥{Number(selectedReagentForDetail.totalPrice).toFixed(2)}</div>
                  )}
                </div>
              </div>
            </div>

            <div>
              <Title level={5} className="!mb-2">记录信息</Title>
              <div className="space-y-2 text-sm">
                <div><span className="text-gray-600">创建时间:</span> {new Date(selectedReagentForDetail.createdAt).toLocaleString()}</div>
                <div><span className="text-gray-600">更新时间:</span> {new Date(selectedReagentForDetail.updatedAt).toLocaleString()}</div>
                <div><span className="text-gray-600">状态:</span> {selectedReagentForDetail.isActive ? '激活' : '停用'}</div>
              </div>
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
});
