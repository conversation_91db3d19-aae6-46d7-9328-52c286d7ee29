import React from 'react';
import {
  Modal,
  Typography,
  Divider,
  Row,
  Col,
  Statistic,
  Table,
  Tag,
  Alert,
  Space,
  Button
} from 'antd';
import {
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  InfoCircleOutlined,
  SaveOutlined,
  CloseOutlined
} from '@ant-design/icons';
import { BatchStockInItem } from './BatchStockInManager';
import { CATEGORY_LABELS } from '@haocai/shared/types/reagent';

const { Title, Text } = Typography;

/**
 * 入库确认对话框组件属性
 */
interface StockInConfirmDialogProps {
  visible: boolean;
  items: BatchStockInItem[];
  onConfirm?: () => void;
  onCancel?: () => void;
  loading?: boolean;
}

/**
 * 入库确认对话框组件
 * 显示入库汇总信息，确认后执行入库操作
 */
export const StockInConfirmDialog: React.FC<StockInConfirmDialogProps> = ({
  visible,
  items,
  onConfirm,
  onCancel,
  loading = false
}) => {
  // 计算汇总统计
  const summary = {
    totalItems: items.length,
    totalQuantity: items.reduce((sum, item) => sum + item.quantity, 0),
    categories: [...new Set(items.map(item => item.reagent.category))],
    suppliers: [...new Set(items.map(item => item.reagent.supplier).filter(Boolean))],
    withBatchNumber: items.filter(item => item.batchNumber).length,
    withExpiryDate: items.filter(item => item.expiryDate).length,
    estimatedValue: items.reduce((sum, item) => {
      const unitPrice = Number(item.reagent.unitPrice) || 0;
      return sum + (unitPrice * item.quantity);
    }, 0)
  };

  // 检查潜在问题
  const warnings = [];
  
  // 检查是否有试剂会超过最大容量
  const capacityWarnings = items.filter(item => {
    if (!item.reagent.maxCapacity) return false;
    const newStock = Number(item.reagent.currentStock) + item.quantity;
    return newStock > Number(item.reagent.maxCapacity);
  });
  
  if (capacityWarnings.length > 0) {
    warnings.push({
      type: 'warning' as const,
      message: `${capacityWarnings.length} 个试剂入库后将超过最大容量`,
      description: '请确认是否继续入库操作'
    });
  }

  // 检查是否有大量入库
  const largeQuantityItems = items.filter(item => {
    const currentStock = Number(item.reagent.currentStock);
    return item.quantity > currentStock * 2; // 入库量超过当前库存2倍
  });

  if (largeQuantityItems.length > 0) {
    warnings.push({
      type: 'info' as const,
      message: `${largeQuantityItems.length} 个试剂入库量较大`,
      description: '入库量超过当前库存的2倍，请确认数量是否正确'
    });
  }

  // 表格列定义
  const columns = [
    {
      title: '试剂名称',
      key: 'name',
      render: (record: BatchStockInItem) => (
        <div>
          <div className="font-medium">{record.reagent.name}</div>
          <div className="text-sm text-gray-500">{record.reagent.code}</div>
        </div>
      ),
      width: 200
    },
    {
      title: '分类',
      key: 'category',
      render: (record: BatchStockInItem) => (
        <Tag color="blue">
          {CATEGORY_LABELS[record.reagent.category]}
        </Tag>
      ),
      width: 120
    },
    {
      title: '当前库存',
      key: 'currentStock',
      render: (record: BatchStockInItem) => (
        <Text>{record.reagent.currentStock} {record.reagent.unit}</Text>
      ),
      width: 100
    },
    {
      title: '入库数量',
      key: 'quantity',
      render: (record: BatchStockInItem) => (
        <Text strong className="text-green-600">
          +{record.quantity} {record.reagent.unit}
        </Text>
      ),
      width: 100
    },
    {
      title: '入库后库存',
      key: 'newStock',
      render: (record: BatchStockInItem) => {
        const newStock = Number(record.reagent.currentStock) + record.quantity;
        const isOverCapacity = record.reagent.maxCapacity && newStock > Number(record.reagent.maxCapacity);
        
        return (
          <Text className={isOverCapacity ? 'text-orange-500' : 'text-blue-600'}>
            {newStock} {record.reagent.unit}
            {isOverCapacity && (
              <ExclamationCircleOutlined className="ml-1 text-orange-500" />
            )}
          </Text>
        );
      },
      width: 120
    },
    {
      title: '批次号',
      key: 'batchNumber',
      render: (record: BatchStockInItem) => (
        <Text>{record.batchNumber || '-'}</Text>
      ),
      width: 120
    },
    {
      title: '入库原因',
      key: 'reason',
      render: (record: BatchStockInItem) => (
        <Text>{record.reason}</Text>
      ),
      width: 150
    }
  ];

  return (
    <Modal
      title={
        <Space>
          <CheckCircleOutlined className="text-green-500" />
          确认入库操作
        </Space>
      }
      open={visible}
      onCancel={onCancel}
      width={1200}
      footer={[
        <Button key="cancel" onClick={onCancel} disabled={loading}>
          <CloseOutlined />
          取消
        </Button>,
        <Button
          key="confirm"
          type="primary"
          onClick={onConfirm}
          loading={loading}
          icon={<SaveOutlined />}
        >
          确认入库
        </Button>
      ]}
      className="stock-in-confirm-dialog"
    >
      <div className="space-y-6">
        {/* 汇总统计 */}
        <div>
          <Title level={5} className="!mb-4">入库汇总</Title>
          <Row gutter={[16, 16]}>
            <Col xs={12} sm={6}>
              <Statistic
                title="试剂数量"
                value={summary.totalItems}
                suffix="个"
                valueStyle={{ color: '#1890ff' }}
              />
            </Col>
            <Col xs={12} sm={6}>
              <Statistic
                title="总入库量"
                value={summary.totalQuantity}
                precision={2}
                valueStyle={{ color: '#52c41a' }}
              />
            </Col>
            <Col xs={12} sm={6}>
              <Statistic
                title="涉及分类"
                value={summary.categories.length}
                suffix="个"
                valueStyle={{ color: '#722ed1' }}
              />
            </Col>
            <Col xs={12} sm={6}>
              <Statistic
                title="预估价值"
                value={summary.estimatedValue}
                precision={2}
                prefix="¥"
                valueStyle={{ color: '#fa8c16' }}
              />
            </Col>
          </Row>
        </div>

        {/* 警告信息 */}
        {warnings.length > 0 && (
          <div className="space-y-3">
            {warnings.map((warning, index) => (
              <Alert
                key={index}
                message={warning.message}
                description={warning.description}
                type={warning.type}
                showIcon
              />
            ))}
          </div>
        )}

        {/* 详细信息 */}
        <div>
          <Title level={5} className="!mb-4">入库明细</Title>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <div className="bg-gray-50 p-3 rounded">
              <Text type="secondary">涉及分类:</Text>
              <div className="mt-1">
                {summary.categories.map(category => (
                  <Tag key={category} color="blue" className="mb-1">
                    {CATEGORY_LABELS[category]}
                  </Tag>
                ))}
              </div>
            </div>
            
            <div className="bg-gray-50 p-3 rounded">
              <Text type="secondary">涉及供应商:</Text>
              <div className="mt-1">
                {summary.suppliers.length > 0 ? (
                  summary.suppliers.map(supplier => (
                    <Tag key={supplier} className="mb-1">
                      {supplier}
                    </Tag>
                  ))
                ) : (
                  <Text type="secondary">无</Text>
                )}
              </div>
            </div>
            
            <div className="bg-gray-50 p-3 rounded">
              <Text type="secondary">批次信息:</Text>
              <div className="mt-1 space-y-1">
                <div>
                  <Text className="text-sm">
                    有批次号: {summary.withBatchNumber} 个
                  </Text>
                </div>
                <div>
                  <Text className="text-sm">
                    有过期日期: {summary.withExpiryDate} 个
                  </Text>
                </div>
              </div>
            </div>
          </div>
        </div>

        <Divider />

        {/* 入库明细表格 */}
        <Table
          columns={columns}
          dataSource={items}
          rowKey="id"
          pagination={false}
          scroll={{ x: 1000 }}
          size="small"
        />

        {/* 操作提示 */}
        <Alert
          message="请仔细核对入库信息"
          description="确认后将执行入库操作，库存数据将被更新且无法撤销。请确保所有信息准确无误。"
          type="info"
          showIcon
          icon={<InfoCircleOutlined />}
        />
      </div>
    </Modal>
  );
};
