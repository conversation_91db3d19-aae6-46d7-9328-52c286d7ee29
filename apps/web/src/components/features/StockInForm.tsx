import React, { useState } from 'react';
import {
  Form,
  Input,
  InputNumber,
  DatePicker,
  Button,
  Card,
  Typography,
  Space,
  Alert,
  Divider,
  Row,
  Col,
  Tag,
  Tooltip
} from 'antd';
import {
  SaveOutlined,
  ClearOutlined,
  InfoCircleOutlined,
  CalendarOutlined,
  NumberOutlined,
  FileTextOutlined
} from '@ant-design/icons';
import { ReagentWithStatus, CATEGORY_LABELS } from '@haocai/shared/types/reagent';
import dayjs, { Dayjs } from 'dayjs';

const { Title, Text } = Typography;
const { TextArea } = Input;

/**
 * 入库表单数据接口
 */
export interface StockInFormData {
  reagentId: string;
  quantity: number;
  batchNumber?: string;
  expiryDate?: Date;
  reason: string;
  notes?: string;
}

/**
 * 入库表单组件属性
 */
interface StockInFormProps {
  reagent: ReagentWithStatus;
  initialData?: Partial<StockInFormData>;
  onSubmit?: (data: StockInFormData) => void;
  onCancel?: () => void;
  loading?: boolean;
  className?: string;
}

/**
 * 入库表单组件
 * 用于填写单个试剂的入库信息
 */
export const StockInForm: React.FC<StockInFormProps> = React.memo(({
  reagent,
  initialData,
  onSubmit,
  onCancel,
  loading = false,
  className = ''
}) => {
  const [form] = Form.useForm();
  const [formData, setFormData] = useState<Partial<StockInFormData>>(initialData || {});

  // 表单验证规则
  const validationRules = {
    quantity: [
      { required: true, message: '请输入入库数量' },
      { type: 'number' as const, min: 0.01, message: '入库数量必须大于0' }
    ],
    batchNumber: [
      { max: 50, message: '批次号不能超过50个字符' },
      { pattern: /^[A-Za-z0-9\-_]+$/, message: '批次号只能包含字母、数字、横线和下划线' }
    ],
    expiryDate: [
      {
        validator: (_: any, value: Dayjs) => {
          if (value && value.isBefore(dayjs(), 'day')) {
            return Promise.reject(new Error('过期日期不能早于今天'));
          }
          return Promise.resolve();
        }
      }
    ],
    reason: [
      { required: true, message: '请输入入库原因' },
      { min: 2, max: 200, message: '入库原因长度应在2-200个字符之间' }
    ],
    notes: [
      { max: 500, message: '备注不能超过500个字符' }
    ]
  };

  // 处理表单提交
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      const submitData: StockInFormData = {
        reagentId: reagent.id,
        quantity: values.quantity,
        batchNumber: values.batchNumber?.trim(),
        expiryDate: values.expiryDate?.toDate(),
        reason: values.reason.trim(),
        notes: values.notes?.trim()
      };

      if (onSubmit) {
        onSubmit(submitData);
      }
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  // 处理表单重置
  const handleReset = () => {
    form.resetFields();
    setFormData(initialData || {});
  };

  // 处理取消
  const handleCancel = () => {
    if (onCancel) {
      onCancel();
    }
  };

  // 计算入库后库存
  const calculateNewStock = (quantity: number) => {
    return Number(reagent.currentStock) + quantity;
  };

  // 检查是否会超过最大容量
  const checkCapacityWarning = (quantity: number) => {
    if (!reagent.maxCapacity) return false;
    const newStock = calculateNewStock(quantity);
    return newStock > Number(reagent.maxCapacity);
  };

  return (
    <div className={`stock-in-form ${className}`}>
      <Card>
        {/* 试剂信息展示 */}
        <div className="mb-6">
          <Title level={4} className="!mb-3">
            入库试剂信息
          </Title>
          <div className="bg-gray-50 p-4 rounded-lg">
            <Row gutter={[16, 8]}>
              <Col xs={24} sm={12}>
                <Text strong>试剂名称:</Text> {reagent.name}
              </Col>
              <Col xs={24} sm={12}>
                <Text strong>试剂编码:</Text> {reagent.code}
              </Col>
              <Col xs={24} sm={12}>
                <Text strong>规格:</Text> {reagent.specification || '未指定'}
              </Col>
              <Col xs={24} sm={12}>
                <Text strong>单位:</Text> {reagent.unit}
              </Col>
              <Col xs={24} sm={12}>
                <Text strong>当前库存:</Text> {reagent.currentStock} {reagent.unit}
              </Col>
              <Col xs={24} sm={12}>
                <Text strong>分类:</Text>
                <Tag color="blue" className="ml-2">
                  {CATEGORY_LABELS[reagent.category]}
                </Tag>
              </Col>
            </Row>
          </div>
        </div>

        <Divider />

        {/* 入库表单 */}
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            quantity: initialData?.quantity,
            batchNumber: initialData?.batchNumber,
            expiryDate: initialData?.expiryDate ? dayjs(initialData.expiryDate) : undefined,
            reason: initialData?.reason,
            notes: initialData?.notes
          }}
          onValuesChange={(_, allValues) => {
            setFormData(allValues);
          }}
        >
          <Row gutter={[16, 0]}>
            {/* 入库数量 */}
            <Col xs={24} sm={12}>
              <Form.Item
                name="quantity"
                label={
                  <Space>
                    <NumberOutlined />
                    入库数量
                    <Tooltip title="请输入要入库的试剂数量，必须大于0">
                      <InfoCircleOutlined className="text-gray-400" />
                    </Tooltip>
                  </Space>
                }
                rules={validationRules.quantity}
              >
                <InputNumber
                  placeholder="请输入入库数量"
                  min={0.01}
                  precision={2}
                  addonAfter={reagent.unit}
                  className="w-full"
                  size="large"
                />
              </Form.Item>

              {/* 库存预警提示 */}
              {formData.quantity && (
                <div className="mb-4">
                  <Alert
                    message={
                      <div>
                        <Text>入库后库存: </Text>
                        <Text strong className="text-green-600">
                          {calculateNewStock(formData.quantity)} {reagent.unit}
                        </Text>
                        {checkCapacityWarning(formData.quantity) && (
                          <Text className="text-orange-500 ml-2">
                            (将超过最大容量 {reagent.maxCapacity} {reagent.unit})
                          </Text>
                        )}
                      </div>
                    }
                    type={checkCapacityWarning(formData.quantity) ? 'warning' : 'info'}
                    showIcon
                    className="text-sm"
                  />
                </div>
              )}
            </Col>

            {/* 批次号 */}
            <Col xs={24} sm={12}>
              <Form.Item
                name="batchNumber"
                label={
                  <Space>
                    <FileTextOutlined />
                    批次号
                    <Text type="secondary">(可选)</Text>
                  </Space>
                }
                rules={validationRules.batchNumber}
              >
                <Input
                  placeholder="请输入批次号"
                  maxLength={50}
                  size="large"
                />
              </Form.Item>
            </Col>

            {/* 过期日期 */}
            <Col xs={24} sm={12}>
              <Form.Item
                name="expiryDate"
                label={
                  <Space>
                    <CalendarOutlined />
                    过期日期
                    <Text type="secondary">(可选)</Text>
                  </Space>
                }
                rules={validationRules.expiryDate}
              >
                <DatePicker
                  placeholder="请选择过期日期"
                  className="w-full"
                  size="large"
                  disabledDate={(current) => current && current < dayjs().startOf('day')}
                />
              </Form.Item>
            </Col>

            {/* 入库原因 */}
            <Col xs={24} sm={12}>
              <Form.Item
                name="reason"
                label={
                  <Space>
                    <InfoCircleOutlined />
                    入库原因
                  </Space>
                }
                rules={validationRules.reason}
              >
                <Input
                  placeholder="请输入入库原因，如：新采购、补充库存等"
                  maxLength={200}
                  size="large"
                />
              </Form.Item>
            </Col>

            {/* 备注 */}
            <Col xs={24}>
              <Form.Item
                name="notes"
                label={
                  <Space>
                    <FileTextOutlined />
                    备注
                    <Text type="secondary">(可选)</Text>
                  </Space>
                }
                rules={validationRules.notes}
              >
                <TextArea
                  placeholder="请输入备注信息..."
                  rows={3}
                  maxLength={500}
                  showCount
                />
              </Form.Item>
            </Col>
          </Row>

          {/* 操作按钮 */}
          <div className="flex justify-end gap-3 pt-4 border-t border-gray-200">
            <Button
              icon={<ClearOutlined />}
              onClick={handleReset}
              disabled={loading}
            >
              重置
            </Button>
            <Button
              onClick={handleCancel}
              disabled={loading}
            >
              取消
            </Button>
            <Button
              type="primary"
              icon={<SaveOutlined />}
              onClick={handleSubmit}
              loading={loading}
            >
              确认入库
            </Button>
          </div>
        </Form>
      </Card>
    </div>
  );
});
