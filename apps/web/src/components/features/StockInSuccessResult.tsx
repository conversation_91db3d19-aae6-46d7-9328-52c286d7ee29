import React from 'react';
import {
  Result,
  Card,
  Typography,
  Button,
  Table,
  Tag,
  Space,
  Statistic,
  Row,
  Col,
  Divider,
  Timeline,
  Alert
} from 'antd';
import {
  CheckCircleOutlined,
  PlusOutlined,
  HomeOutlined,
  PrinterOutlined,
  DownloadOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import { BatchStockInItem } from './BatchStockInManager';
import { CATEGORY_LABELS } from '@haocai/shared/types/reagent';

const { Title, Text } = Typography;

/**
 * 入库结果数据接口
 */
export interface StockInResult {
  success: boolean;
  message: string;
  transactionId?: string;
  timestamp: Date;
  items: BatchStockInItem[];
  summary: {
    totalItems: number;
    totalQuantity: number;
    totalValue: number;
    affectedCategories: string[];
  };
}

/**
 * 入库成功结果组件属性
 */
interface StockInSuccessResultProps {
  result: StockInResult;
  onContinueStockIn?: () => void;
  onGoToDashboard?: () => void;
  onPrintReport?: () => void;
  onDownloadReport?: () => void;
  className?: string;
}

/**
 * 入库成功结果组件
 * 显示入库操作的成功结果和后续操作选项
 */
export const StockInSuccessResult: React.FC<StockInSuccessResultProps> = ({
  result,
  onContinueStockIn,
  onGoToDashboard,
  onPrintReport,
  onDownloadReport,
  className = ''
}) => {
  // 表格列定义
  const columns = [
    {
      title: '试剂名称',
      key: 'name',
      render: (record: BatchStockInItem) => (
        <div>
          <div className="font-medium">{record.reagent.name}</div>
          <div className="text-sm text-gray-500">{record.reagent.code}</div>
        </div>
      ),
      width: 200
    },
    {
      title: '分类',
      key: 'category',
      render: (record: BatchStockInItem) => (
        <Tag color="blue">
          {CATEGORY_LABELS[record.reagent.category]}
        </Tag>
      ),
      width: 120
    },
    {
      title: '入库前库存',
      key: 'previousStock',
      render: (record: BatchStockInItem) => (
        <Text>{record.reagent.currentStock} {record.reagent.unit}</Text>
      ),
      width: 120
    },
    {
      title: '入库数量',
      key: 'quantity',
      render: (record: BatchStockInItem) => (
        <Text strong className="text-green-600">
          +{record.quantity} {record.reagent.unit}
        </Text>
      ),
      width: 100
    },
    {
      title: '入库后库存',
      key: 'newStock',
      render: (record: BatchStockInItem) => {
        const newStock = Number(record.reagent.currentStock) + record.quantity;
        return (
          <Text strong className="text-blue-600">
            {newStock} {record.reagent.unit}
          </Text>
        );
      },
      width: 120
    },
    {
      title: '批次号',
      key: 'batchNumber',
      render: (record: BatchStockInItem) => (
        <Text>{record.batchNumber || '-'}</Text>
      ),
      width: 120
    },
    {
      title: '状态',
      key: 'status',
      render: () => (
        <Tag color="success" icon={<CheckCircleOutlined />}>
          入库成功
        </Tag>
      ),
      width: 100
    }
  ];

  return (
    <div className={`stock-in-success-result ${className}`}>
      {/* 成功结果展示 */}
      <Result
        status="success"
        title="入库操作成功完成！"
        subTitle={
          <div className="space-y-2">
            <div>
              成功入库 {result.summary.totalItems} 个试剂，
              总数量 {result.summary.totalQuantity.toFixed(2)}，
              预估价值 ¥{result.summary.totalValue.toFixed(2)}
            </div>
            {result.transactionId && (
              <div className="text-sm text-gray-500">
                交易ID: {result.transactionId}
              </div>
            )}
            <div className="text-sm text-gray-500">
              操作时间: {result.timestamp.toLocaleString()}
            </div>
          </div>
        }
        extra={[
          <Button
            key="continue"
            type="primary"
            icon={<PlusOutlined />}
            onClick={onContinueStockIn}
            size="large"
          >
            继续入库
          </Button>,
          <Button
            key="dashboard"
            icon={<HomeOutlined />}
            onClick={onGoToDashboard}
            size="large"
          >
            返回仪表板
          </Button>
        ]}
      />

      {/* 操作统计 */}
      <Card className="mb-6">
        <Title level={4} className="!mb-4">操作统计</Title>
        <Row gutter={[16, 16]}>
          <Col xs={12} sm={6}>
            <Statistic
              title="成功入库试剂"
              value={result.summary.totalItems}
              suffix="个"
              valueStyle={{ color: '#52c41a' }}
            />
          </Col>
          <Col xs={12} sm={6}>
            <Statistic
              title="总入库数量"
              value={result.summary.totalQuantity}
              precision={2}
              valueStyle={{ color: '#1890ff' }}
            />
          </Col>
          <Col xs={12} sm={6}>
            <Statistic
              title="涉及分类"
              value={result.summary.affectedCategories.length}
              suffix="个"
              valueStyle={{ color: '#722ed1' }}
            />
          </Col>
          <Col xs={12} sm={6}>
            <Statistic
              title="预估价值"
              value={result.summary.totalValue}
              precision={2}
              prefix="¥"
              valueStyle={{ color: '#fa8c16' }}
            />
          </Col>
        </Row>
      </Card>

      {/* 操作时间线 */}
      <Card className="mb-6">
        <Title level={4} className="!mb-4">操作记录</Title>
        <Timeline
          items={[
            {
              color: 'blue',
              children: (
                <div>
                  <Text strong>开始入库操作</Text>
                  <div className="text-sm text-gray-500 mt-1">
                    选择了 {result.summary.totalItems} 个试剂进行入库
                  </div>
                </div>
              )
            },
            {
              color: 'green',
              children: (
                <div>
                  <Text strong>验证入库信息</Text>
                  <div className="text-sm text-gray-500 mt-1">
                    检查数量、批次号、过期日期等信息
                  </div>
                </div>
              )
            },
            {
              color: 'green',
              children: (
                <div>
                  <Text strong>更新库存数据</Text>
                  <div className="text-sm text-gray-500 mt-1">
                    成功更新 {result.summary.totalItems} 个试剂的库存信息
                  </div>
                </div>
              )
            },
            {
              color: 'green',
              children: (
                <div>
                  <Text strong>入库完成</Text>
                  <div className="text-sm text-gray-500 mt-1">
                    {result.timestamp.toLocaleString()}
                  </div>
                </div>
              )
            }
          ]}
        />
      </Card>

      {/* 入库明细 */}
      <Card>
        <div className="flex justify-between items-center mb-4">
          <Title level={4} className="!mb-0">入库明细</Title>
          <Space>
            <Button
              icon={<PrinterOutlined />}
              onClick={onPrintReport}
            >
              打印报告
            </Button>
            <Button
              icon={<DownloadOutlined />}
              onClick={onDownloadReport}
            >
              下载报告
            </Button>
          </Space>
        </div>

        <Table
          columns={columns}
          dataSource={result.items}
          rowKey="id"
          pagination={false}
          scroll={{ x: 1000 }}
          size="middle"
        />
      </Card>

      {/* 后续操作提示 */}
      <Alert
        message="入库操作已完成"
        description={
          <div className="space-y-2">
            <div>
              库存数据已实时更新，您可以在试剂搜索页面查看最新的库存信息。
            </div>
            <div>
              建议定期检查库存警戒线，确保试剂供应充足。
            </div>
          </div>
        }
        type="success"
        showIcon
        className="mt-6"
        action={
          <Space direction="vertical">
            <Button
              size="small"
              type="primary"
              icon={<PlusOutlined />}
              onClick={onContinueStockIn}
            >
              继续入库
            </Button>
            <Button
              size="small"
              icon={<ReloadOutlined />}
              onClick={() => window.location.reload()}
            >
              刷新页面
            </Button>
          </Space>
        }
      />
    </div>
  );
};
